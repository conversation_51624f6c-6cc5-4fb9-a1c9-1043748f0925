import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, MessageSquare, RotateCcw, <PERSON>rk<PERSON> } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { useUserPersonalization } from '@/hooks/useUserPersonalization';
import { useConversationContext } from '@/hooks/useConversationContext';

interface UserPreferencesProps {
  className?: string;
}

const UserPreferences: React.FC<UserPreferencesProps> = ({ className }) => {
  const {
    preferences,
    learningData,
    updatePreferences,
    getLearningLevel,
    getRecommendedWords,
    resetPersonalization
  } = useUserPersonalization();

  const { entries, clearContext } = useConversationContext();

  const handlePreferenceChange = (key: keyof typeof preferences, value: any) => {
    updatePreferences({ [key]: value });
  };

  const currentLevel = getLearningLevel();
  const recommendedWords = getRecommendedWords();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center gap-3">
        <Settings className="h-6 w-6 text-maroon" />
        <div>
          <h2 className="text-2xl font-bold text-maroon">User Preferences</h2>
          <p className="text-muted-foreground">Customize your learning experience</p>
        </div>
      </div>

      {/* Learning Profile */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Learning Profile
          </CardTitle>
          <CardDescription>
            Your current learning level and statistics
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-maroon/5 rounded-lg">
              <div className="text-2xl font-bold text-maroon">{currentLevel}</div>
              <div className="text-sm text-muted-foreground">Current Level</div>
            </div>
            <div className="text-center p-4 bg-maroon/5 rounded-lg">
              <div className="text-2xl font-bold text-maroon">{learningData.searchHistory.length}</div>
              <div className="text-sm text-muted-foreground">Words Searched</div>
            </div>
            <div className="text-center p-4 bg-maroon/5 rounded-lg">
              <div className="text-2xl font-bold text-maroon">{learningData.bookmarkedWords.length}</div>
              <div className="text-sm text-muted-foreground">Bookmarked</div>
            </div>
          </div>

          <div>
            <Label className="text-sm font-medium">Learning Level</Label>
            <Select
              value={preferences.learningLevel}
              onValueChange={(value) => handlePreferenceChange('learningLevel', value)}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="beginner">Beginner - Simple explanations</SelectItem>
                <SelectItem value="intermediate">Intermediate - Moderate complexity</SelectItem>
                <SelectItem value="advanced">Advanced - Detailed analysis</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* AI Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            AI Response Preferences
          </CardTitle>
          <CardDescription>
            Customize how AI responds to your questions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label className="text-sm font-medium">Explanation Style</Label>
            <Select
              value={preferences.preferredExplanationStyle}
              onValueChange={(value) => handlePreferenceChange('preferredExplanationStyle', value)}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="simple">Simple - Straightforward explanations</SelectItem>
                <SelectItem value="detailed">Detailed - Comprehensive information</SelectItem>
                <SelectItem value="academic">Academic - Scholarly approach</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-sm font-medium">AI Suggestions</Label>
              <div className="text-sm text-muted-foreground">
                Enable real-time AI-powered word suggestions while typing
              </div>
            </div>
            <Switch
              checked={preferences.aiSuggestionsEnabled}
              onCheckedChange={(checked) => handlePreferenceChange('aiSuggestionsEnabled', checked)}
            />
          </div>

          <div>
            <Label className="text-sm font-medium">Example Style</Label>
            <Select
              value={preferences.examplePreference}
              onValueChange={(value) => handlePreferenceChange('examplePreference', value)}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="formal">Formal - Professional examples</SelectItem>
                <SelectItem value="casual">Casual - Everyday examples</SelectItem>
                <SelectItem value="mixed">Mixed - Both formal and casual</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Conversation Context */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Conversation Context
          </CardTitle>
          <CardDescription>
            Manage your conversation history and context
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium">Active Conversations</div>
              <div className="text-sm text-muted-foreground">
                {entries.length} entries in current session
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={clearContext}
              disabled={entries.length === 0}
            >
              Clear Context
            </Button>
          </div>

          {entries.length > 0 && (
            <div className="space-y-2">
              <div className="text-sm font-medium">Recent Words:</div>
              <div className="flex flex-wrap gap-2">
                {entries.slice(-5).map((entry) => (
                  <Badge key={entry.id} variant="secondary">
                    {entry.word}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recommended Words */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            Recommended for You
          </CardTitle>
          <CardDescription>
            Words suggested based on your learning level
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {recommendedWords.map((word) => (
              <Badge key={word} variant="outline" className="cursor-pointer hover:bg-maroon/10">
                {word}
              </Badge>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Reset Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RotateCcw className="h-5 w-5" />
            Reset Options
          </CardTitle>
          <CardDescription>
            Reset your preferences and learning data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="text-sm text-yellow-800">
                <strong>Warning:</strong> This will reset all your preferences and learning data. This action cannot be undone.
              </div>
            </div>
            <Button
              variant="destructive"
              onClick={resetPersonalization}
              className="w-full"
            >
              Reset All Preferences
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default UserPreferences;