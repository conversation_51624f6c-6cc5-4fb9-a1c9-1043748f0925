import { useState, useCallback, useRef } from 'react';

export interface ConversationEntry {
  id: string;
  word: string;
  userQuery?: string;
  aiResponse: string;
  timestamp: number;
  context: {
    partOfSpeech?: string;
    definition?: string;
    relatedWords?: string[];
    userIntent?: 'definition' | 'example' | 'etymology' | 'usage' | 'clarification';
  };
}

export interface ConversationContext {
  entries: ConversationEntry[];
  currentSession: string;
  addEntry: (entry: Omit<ConversationEntry, 'id' | 'timestamp'>) => void;
  getRecentContext: (limit?: number) => ConversationEntry[];
  getRelatedEntries: (word: string) => ConversationEntry[];
  clearContext: () => void;
  generateContextPrompt: (currentWord: string, userQuery?: string) => string;
}

const MAX_CONTEXT_ENTRIES = 10;
const SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes

export const useConversationContext = (): ConversationContext => {
  const [entries, setEntries] = useState<ConversationEntry[]>([]);
  const [currentSession] = useState(() => `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);
  const lastActivityRef = useRef(Date.now());

  const addEntry = useCallback((entry: Omit<ConversationEntry, 'id' | 'timestamp'>) => {
    const now = Date.now();
    lastActivityRef.current = now;

    // Check if session has timed out
    if (now - lastActivityRef.current > SESSION_TIMEOUT) {
      setEntries([]);
    }

    const newEntry: ConversationEntry = {
      ...entry,
      id: `entry-${now}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: now,
    };

    setEntries(prev => {
      const updated = [...prev, newEntry];
      // Keep only the most recent entries
      return updated.slice(-MAX_CONTEXT_ENTRIES);
    });
  }, []);

  const getRecentContext = useCallback((limit = 5) => {
    return entries.slice(-limit);
  }, [entries]);

  const getRelatedEntries = useCallback((word: string) => {
    return entries.filter(entry =>
      entry.word.toLowerCase().includes(word.toLowerCase()) ||
      entry.context.relatedWords?.some(related =>
        related.toLowerCase().includes(word.toLowerCase())
      )
    );
  }, [entries]);

  const clearContext = useCallback(() => {
    setEntries([]);
  }, []);

  const generateContextPrompt = useCallback((currentWord: string, userQuery?: string) => {
    const recentEntries = getRecentContext(3);
    const relatedEntries = getRelatedEntries(currentWord);

    if (recentEntries.length === 0 && relatedEntries.length === 0) {
      return '';
    }

    let contextPrompt = '\n\nKONTEKSTO NG NAKARAANG USAPAN:\n';

    // Add recent conversation context
    if (recentEntries.length > 0) {
      contextPrompt += 'Mga nakaraang salitang tinalakay:\n';
      recentEntries.forEach(entry => {
        contextPrompt += `- ${entry.word}: ${entry.context.definition || 'walang definition'}\n`;
        if (entry.userQuery) {
          contextPrompt += `  Tanong ng user: ${entry.userQuery}\n`;
        }
      });
    }

    // Add related words context
    if (relatedEntries.length > 0) {
      contextPrompt += '\nMga kaugnay na salitang dating tinalakay:\n';
      relatedEntries.forEach(entry => {
        contextPrompt += `- ${entry.word}: ${entry.context.definition || 'walang definition'}\n`;
      });
    }

    // Add user intent analysis
    if (userQuery) {
      contextPrompt += `\nKasalukuyang tanong ng user: "${userQuery}"\n`;

      // Analyze user intent
      const intent = analyzeUserIntent(userQuery);
      contextPrompt += `Mukhang hinahanap ng user: ${intent}\n`;
    }

    contextPrompt += '\nGamitin ang kontekstong ito para magbigay ng mas relevant at connected na sagot.\n';

    return contextPrompt;
  }, [getRecentContext, getRelatedEntries]);

  return {
    entries,
    currentSession,
    addEntry,
    getRecentContext,
    getRelatedEntries,
    clearContext,
    generateContextPrompt,
  };
};

// Helper function to analyze user intent
const analyzeUserIntent = (query: string): string => {
  const lowerQuery = query.toLowerCase();

  if (lowerQuery.includes('halimbawa') || lowerQuery.includes('example')) {
    return 'mga halimbawa ng paggamit';
  }
  if (lowerQuery.includes('kahulugan') || lowerQuery.includes('ibig sabihin')) {
    return 'kahulugan ng salita';
  }
  if (lowerQuery.includes('etimolohiya') || lowerQuery.includes('pinagmulan')) {
    return 'pinagmulan ng salita';
  }
  if (lowerQuery.includes('paano') || lowerQuery.includes('gamitin')) {
    return 'paano gamitin ang salita';
  }
  if (lowerQuery.includes('kasingkahulugan') || lowerQuery.includes('synonym')) {
    return 'mga kasingkahulugan';
  }
  if (lowerQuery.includes('pagkakaiba') || lowerQuery.includes('difference')) {
    return 'pagkakaiba sa ibang salita';
  }

  return 'general na impormasyon';
};