import { useState, useCallback, useEffect } from 'react';

export interface UserPreferences {
  learningLevel: 'beginner' | 'intermediate' | 'advanced';
  preferredExplanationStyle: 'simple' | 'detailed' | 'academic';
  interests: string[];
  languagePreference: 'filipino' | 'english' | 'mixed';
  difficultyPreference: 'easy' | 'moderate' | 'challenging';
  examplePreference: 'formal' | 'casual' | 'mixed';
}

export interface UserLearningData {
  searchHistory: string[];
  bookmarkedWords: string[];
  frequentlySearchedCategories: string[];
  timeSpentOnWords: Record<string, number>;
  correctAnswersInQuizzes?: number;
  totalQuizAttempts?: number;
  lastActiveDate: number;
  learningStreak: number;
}

export interface PersonalizationContext {
  preferences: UserPreferences;
  learningData: UserLearningData;
  updatePreferences: (newPreferences: Partial<UserPreferences>) => void;
  updateLearningData: (data: Partial<UserLearningData>) => void;
  getPersonalizedPrompt: (word: string, basePrompt: string) => string;
  getLearningLevel: () => 'beginner' | 'intermediate' | 'advanced';
  getRecommendedWords: () => string[];
  resetPersonalization: () => void;
}

const DEFAULT_PREFERENCES: UserPreferences = {
  learningLevel: 'beginner',
  preferredExplanationStyle: 'simple',
  interests: [],
  languagePreference: 'filipino',
  difficultyPreference: 'easy',
  examplePreference: 'casual',
};

const DEFAULT_LEARNING_DATA: UserLearningData = {
  searchHistory: [],
  bookmarkedWords: [],
  frequentlySearchedCategories: [],
  timeSpentOnWords: {},
  lastActiveDate: Date.now(),
  learningStreak: 0,
};

export const useUserPersonalization = (): PersonalizationContext => {
  // Load preferences from localStorage
  const [preferences, setPreferences] = useState<UserPreferences>(() => {
    try {
      const saved = localStorage.getItem('digiksyunaryo-user-preferences');
      return saved ? { ...DEFAULT_PREFERENCES, ...JSON.parse(saved) } : DEFAULT_PREFERENCES;
    } catch {
      return DEFAULT_PREFERENCES;
    }
  });

  // Load learning data from localStorage
  const [learningData, setLearningData] = useState<UserLearningData>(() => {
    try {
      const saved = localStorage.getItem('digiksyunaryo-learning-data');
      return saved ? { ...DEFAULT_LEARNING_DATA, ...JSON.parse(saved) } : DEFAULT_LEARNING_DATA;
    } catch {
      return DEFAULT_LEARNING_DATA;
    }
  });

  // Save preferences to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('digiksyunaryo-user-preferences', JSON.stringify(preferences));
  }, [preferences]);

  // Save learning data to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('digiksyunaryo-learning-data', JSON.stringify(learningData));
  }, [learningData]);

  const updatePreferences = useCallback((newPreferences: Partial<UserPreferences>) => {
    setPreferences(prev => ({ ...prev, ...newPreferences }));
  }, []);

  const updateLearningData = useCallback((data: Partial<UserLearningData>) => {
    setLearningData(prev => ({ ...prev, ...data }));
  }, []);

  const getLearningLevel = useCallback((): 'beginner' | 'intermediate' | 'advanced' => {
    const { searchHistory, bookmarkedWords, timeSpentOnWords } = learningData;
    const totalSearches = searchHistory.length;
    const totalBookmarks = bookmarkedWords.length;
    const avgTimeSpent = Object.values(timeSpentOnWords).reduce((a, b) => a + b, 0) / Object.keys(timeSpentOnWords).length || 0;

    // Auto-detect learning level based on usage patterns
    if (totalSearches > 100 && totalBookmarks > 20 && avgTimeSpent > 30000) {
      return 'advanced';
    } else if (totalSearches > 30 && totalBookmarks > 5 && avgTimeSpent > 15000) {
      return 'intermediate';
    }

    return preferences.learningLevel;
  }, [learningData, preferences.learningLevel]);

  const getPersonalizedPrompt = useCallback((word: string, basePrompt: string): string => {
    const level = getLearningLevel();
    const { preferredExplanationStyle, languagePreference, examplePreference } = preferences;

    let personalizedPrompt = basePrompt;

    // Add personalization instructions
    personalizedPrompt += '\n\nMGA PERSONALIZED NA INSTRUCTION:\n';

    // Learning level adjustments
    switch (level) {
      case 'beginner':
        personalizedPrompt += '- Gumamit ng simpleng wika at madaling maintindihan na mga salita\n';
        personalizedPrompt += '- Magbigay ng mas detalyadong paliwanag sa mga basic concepts\n';
        personalizedPrompt += '- Iwasan ang masyadong technical na mga termino\n';
        break;
      case 'intermediate':
        personalizedPrompt += '- Gumamit ng moderate na complexity sa paliwanag\n';
        personalizedPrompt += '- Pwedeng magdagdag ng kaunting technical terms pero i-explain pa rin\n';
        personalizedPrompt += '- Magbigay ng comparative examples sa ibang salita\n';
        break;
      case 'advanced':
        personalizedPrompt += '- Pwedeng gumamit ng advanced vocabulary at concepts\n';
        personalizedPrompt += '- Magbigay ng mas malalim na etymological at linguistic analysis\n';
        personalizedPrompt += '- Include cultural at historical context kung applicable\n';
        break;
    }

    // Explanation style preferences
    switch (preferredExplanationStyle) {
      case 'simple':
        personalizedPrompt += '- Gawing simple at straightforward ang mga paliwanag\n';
        break;
      case 'detailed':
        personalizedPrompt += '- Magbigay ng comprehensive at detailed na explanation\n';
        break;
      case 'academic':
        personalizedPrompt += '- Gumamit ng academic tone at scholarly approach\n';
        break;
    }

    // Example preferences
    switch (examplePreference) {
      case 'formal':
        personalizedPrompt += '- Gumamit ng formal na mga halimbawa sa pangungusap\n';
        break;
      case 'casual':
        personalizedPrompt += '- Gumamit ng casual, everyday na mga halimbawa\n';
        break;
      case 'mixed':
        personalizedPrompt += '- Magbigay ng both formal at casual na mga halimbawa\n';
        break;
    }

    return personalizedPrompt;
  }, [preferences, getLearningLevel]);

  const getRecommendedWords = useCallback((): string[] => {
    const { searchHistory, frequentlySearchedCategories } = learningData;
    const level = getLearningLevel();

    // This would ideally connect to your word database
    // For now, return some sample recommendations based on level
    const recommendations: Record<string, string[]> = {
      beginner: ['aura', 'vibe', 'slay', 'bussin', 'cap'],
      intermediate: ['rizz', 'sigma', 'chad alpha', 'brainrot', 'delulu'],
      advanced: ['skibidi', 'mewing', 'mogging', 'fanum tax', 'ohio']
    };

    return recommendations[level] || recommendations.beginner;
  }, [learningData, getLearningLevel]);

  const resetPersonalization = useCallback(() => {
    setPreferences(DEFAULT_PREFERENCES);
    setLearningData(DEFAULT_LEARNING_DATA);
    localStorage.removeItem('digiksyunaryo-user-preferences');
    localStorage.removeItem('digiksyunaryo-learning-data');
  }, []);

  return {
    preferences,
    learningData,
    updatePreferences,
    updateLearningData,
    getPersonalizedPrompt,
    getLearningLevel,
    getRecommendedWords,
    resetPersonalization,
  };
};