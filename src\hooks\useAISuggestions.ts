import { useState, useCallback, useRef, useEffect } from 'react';
import { detailedWordData } from '@/data/dictionary';

export interface AISuggestion {
  word: string;
  confidence: number;
  reason: string;
  category: 'spelling' | 'semantic' | 'phonetic' | 'contextual';
}

export interface AISuggestionsContext {
  suggestions: AISuggestion[];
  isLoading: boolean;
  error: string | null;
  getSuggestions: (query: string, context?: string) => Promise<void>;
  clearSuggestions: () => void;
}

const GEMINI_API_KEY = import.meta.env.VITE_GEMINI_API_KEY;
const GEMINI_MODEL = import.meta.env.VITE_GEMINI_MODEL || "gemini-2.0-flash-lite";
const DEBOUNCE_DELAY = 300; // ms
const MIN_QUERY_LENGTH = 2;
const MAX_SUGGESTIONS = 5;

export const useAISuggestions = (): AISuggestionsContext => {
  const [suggestions, setSuggestions] = useState<AISuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  const clearSuggestions = useCallback(() => {
    setSuggestions([]);
    setError(null);
  }, []);

  const getLocalSuggestions = useCallback((query: string): AISuggestion[] => {
    const lowerQuery = query.toLowerCase();
    const localSuggestions: AISuggestion[] = [];

    // Get exact matches and partial matches from dictionary
    Object.keys(detailedWordData).forEach(word => {
      const lowerWord = word.toLowerCase();

      if (lowerWord.startsWith(lowerQuery)) {
        localSuggestions.push({
          word,
          confidence: 0.9,
          reason: 'Exact prefix match',
          category: 'spelling'
        });
      } else if (lowerWord.includes(lowerQuery)) {
        localSuggestions.push({
          word,
          confidence: 0.7,
          reason: 'Contains query',
          category: 'spelling'
        });
      } else if (calculateLevenshteinDistance(lowerQuery, lowerWord) <= 2) {
        localSuggestions.push({
          word,
          confidence: 0.6,
          reason: 'Similar spelling',
          category: 'spelling'
        });
      }
    });

    // Sort by confidence and return top results
    return localSuggestions
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, MAX_SUGGESTIONS);
  }, []);

  const getAISuggestions = useCallback(async (query: string, context?: string): Promise<AISuggestion[]> => {
    if (!GEMINI_API_KEY) {
      throw new Error('AI API key not configured');
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    const prompt = `
Ikaw ay isang AI assistant na tumutulong sa paghahanap ng mga salitang Filipino.

QUERY NG USER: "${query}"
${context ? `CONTEXT: ${context}` : ''}

AVAILABLE WORDS SA DATABASE:
${Object.keys(detailedWordData).slice(0, 50).join(', ')}...

INSTRUCTIONS:
1. Magbigay ng 3-5 na mga suggested words na maaaring hinahanap ng user
2. I-consider ang spelling mistakes, phonetic similarities, at semantic relationships
3. Magbigay ng confidence score (0.1-1.0) para sa bawat suggestion
4. Ipaliwanag kung bakit mo ni-recommend ang bawat word

FORMAT NG RESPONSE:
SUGGESTION 1: [word] | CONFIDENCE: [0.1-1.0] | REASON: [explanation] | CATEGORY: [spelling/semantic/phonetic/contextual]
SUGGESTION 2: [word] | CONFIDENCE: [0.1-1.0] | REASON: [explanation] | CATEGORY: [spelling/semantic/phonetic/contextual]
...

Gumamit lang ng mga salitang available sa database na nabanggit sa itaas.
`;

    const endpoint = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_MODEL}:generateContent`;
    const url = new URL(endpoint);
    url.searchParams.append("key", GEMINI_API_KEY);

    const response = await fetch(url.toString(), {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.3,
          topP: 0.8,
          topK: 20,
          maxOutputTokens: 512
        },
      }),
      signal: abortControllerRef.current.signal
    });

    if (!response.ok) {
      throw new Error(`AI API error: ${response.status}`);
    }

    const result = await response.json();

    if (!result.candidates || result.candidates.length === 0) {
      throw new Error('No AI response generated');
    }

    const aiResponse = result.candidates[0]?.content?.parts?.[0]?.text?.trim() || "";

    // Parse AI response
    const suggestions: AISuggestion[] = [];
    const lines = aiResponse.split('\n');

    lines.forEach(line => {
      const match = line.match(/SUGGESTION \d+: (.+?) \| CONFIDENCE: ([\d.]+) \| REASON: (.+?) \| CATEGORY: (\w+)/);
      if (match) {
        const [, word, confidence, reason, category] = match;
        suggestions.push({
          word: word.trim(),
          confidence: parseFloat(confidence),
          reason: reason.trim(),
          category: category.trim() as AISuggestion['category']
        });
      }
    });

    return suggestions;
  }, []);

  const getSuggestions = useCallback(async (query: string, context?: string) => {
    if (query.length < MIN_QUERY_LENGTH) {
      clearSuggestions();
      return;
    }

    // Clear previous timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Debounce the request
    debounceTimeoutRef.current = setTimeout(async () => {
      setIsLoading(true);
      setError(null);

      try {
        // First, get local suggestions for immediate feedback
        const localSuggestions = getLocalSuggestions(query);
        setSuggestions(localSuggestions);

        // Then, try to get AI suggestions for better results
        if (GEMINI_API_KEY && query.length >= 3) {
          try {
            const aiSuggestions = await getAISuggestions(query, context);

            // Combine and deduplicate suggestions
            const combinedSuggestions = [...aiSuggestions];

            // Add local suggestions that aren't already in AI suggestions
            localSuggestions.forEach(localSug => {
              if (!aiSuggestions.some(aiSug => aiSug.word.toLowerCase() === localSug.word.toLowerCase())) {
                combinedSuggestions.push(localSug);
              }
            });

            // Sort by confidence and limit results
            const finalSuggestions = combinedSuggestions
              .sort((a, b) => b.confidence - a.confidence)
              .slice(0, MAX_SUGGESTIONS);

            setSuggestions(finalSuggestions);
          } catch (aiError) {
            // If AI fails, keep the local suggestions
            console.warn('AI suggestions failed, using local suggestions:', aiError);
          }
        }
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to get suggestions');
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    }, DEBOUNCE_DELAY);
  }, [getLocalSuggestions, getAISuggestions, clearSuggestions]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    suggestions,
    isLoading,
    error,
    getSuggestions,
    clearSuggestions,
  };
};

// Helper function to calculate Levenshtein distance
const calculateLevenshteinDistance = (str1: string, str2: string): number => {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) {
    matrix[0][i] = i;
  }

  for (let j = 0; j <= str2.length; j++) {
    matrix[j][0] = j;
  }

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
};